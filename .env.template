# BetSightly Backend Environment Configuration Template
# Copy this file to .env and fill in your API keys

# =============================================================================
# API KEYS (Required for live predictions)
# =============================================================================

# Football-Data.org API Key (Primary data source for live fixtures)
# Get your free API key at: https://www.football-data.org/client/register
FOOTBALL_DATA_KEY=

# API-Football Key (Alternative data source)
# Get your free API key at: https://rapidapi.com/api-sports/api/api-football
API_FOOTBALL_KEY=

# =============================================================================
# DATA SOURCE CONFIGURATION
# =============================================================================

# GitHub dataset URL for training (default is provided)
DATA_GITHUB_DATASET_URL=https://raw.githubusercontent.com/xgabora/Club-Football-Match-Data-2000-2025/main/data/Matches.csv

# Local data directories
DATA_DATA_DIR=data
DATA_MODELS_DIR=models
DATA_CACHE_DIR=cache

# =============================================================================
# ML MODEL CONFIGURATION
# =============================================================================

# Model priorities (comma-separated, in order of preference)
ML_PREFERRED_MODELS=xgboost,lightgbm,neural_network,lstm,ensemble

# Training parameters
ML_TRAIN_TEST_SPLIT=0.2
ML_CROSS_VALIDATION_FOLDS=5
ML_HYPERPARAMETER_OPTIMIZATION=true

# Feature engineering
ML_FEATURE_SELECTION=true
ML_MIN_FEATURE_IMPORTANCE=0.01

# Prediction filtering
ML_MIN_CONFIDENCE_THRESHOLD=0.65
ML_MAX_PREDICTIONS_PER_CATEGORY=10

# Caching (in seconds)
ML_FEATURE_CACHE_EXPIRY=86400
ML_PREDICTION_CACHE_TTL=1800

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Database URL (SQLite by default)
DATABASE_URL=sqlite:///./football.db
DATABASE_ECHO=false
DATABASE_POOL_SIZE=5
DATABASE_MAX_OVERFLOW=10

# =============================================================================
# API CONFIGURATION
# =============================================================================

# Football-Data.org settings
FOOTBALL_DATA_BASE_URL=https://api.football-data.org/v4
FOOTBALL_DATA_DAILY_LIMIT=100
FOOTBALL_DATA_DEFAULT_COMPETITIONS=PL,PD,SA,BL1,FL1

# API-Football settings
API_FOOTBALL_API_HOST=api-football-v1.p.rapidapi.com
API_FOOTBALL_BASE_URL=https://api-football-v1.p.rapidapi.com/v3
API_FOOTBALL_DAILY_LIMIT=100

# =============================================================================
# TELEGRAM BOT (Optional)
# =============================================================================

# Telegram bot configuration for notifications
TELEGRAM_BOT_TOKEN=
TELEGRAM_CHAT_ID=
TELEGRAM_ENABLED=false

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO
LOG_FILE=logs/betsightly.log
LOG_MAX_SIZE=10485760
LOG_BACKUP_COUNT=5

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================

# Environment mode
ENVIRONMENT=development

# Security settings
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1

# CORS settings
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Rate limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=60

# =============================================================================
# ADVANCED SETTINGS (Optional)
# =============================================================================

# Cache settings
REDIS_URL=redis://localhost:6379/0
CACHE_ENABLED=true
CACHE_DEFAULT_TTL=3600

# Monitoring
SENTRY_DSN=
MONITORING_ENABLED=false

# Performance
ASYNC_WORKERS=4
MAX_CONCURRENT_REQUESTS=100

# =============================================================================
# USAGE INSTRUCTIONS
# =============================================================================

# 1. Copy this file to .env:
#    cp .env.template .env

# 2. Fill in your API keys (at minimum FOOTBALL_DATA_KEY)

# 3. Adjust other settings as needed for your environment

# 4. Run the streamlined ML pipeline:
#    python ml_pipeline_streamlined.py --train-only
#    python ml_pipeline_streamlined.py --date 2023-12-01

# 5. Start the API server:
#    python -m uvicorn main:app --host 127.0.0.1 --port 8000

# =============================================================================
# NOTES
# =============================================================================

# - Only FOOTBALL_DATA_KEY or API_FOOTBALL_KEY is required for basic functionality
# - The system will automatically download and cache the GitHub dataset for training
# - Models will be trained automatically on first run if they don't exist
# - All other settings have sensible defaults and can be left unchanged
# - For production, consider setting up Redis for better caching performance
