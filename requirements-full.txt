# Core FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0

# Pydantic v2 (upgraded from v1)
pydantic==2.5.0
pydantic-settings==2.1.0

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
bcrypt==4.1.2

# HTTP client
aiohttp==3.9.1
httpx==0.25.2
requests==2.31.0

# Data processing (Python 3.12 compatible versions)
pandas==2.2.0
numpy==1.26.0
fastparquet==2024.2.0
joblib==1.3.2
scikit-learn==1.4.0
xgboost==2.0.3
lightgbm==4.3.0
# catboost==1.2.2  # Temporarily disabled - build issues on Railway
# tensorflow==2.15.0  # Neural networks and LSTM models - temporarily disabled for Railway
matplotlib==3.8.0
seaborn==0.13.0
pyarrow==15.0.0

# Model Explainability & Interpretability (Railway compatible versions)
# shap==0.44.0  # Temporarily disabled for Railway deployment
# lime==*******  # Temporarily disabled for Railway deployment
# eli5==0.13.0  # Temporarily disabled for Railway deployment

# Model Performance Optimization (Railway compatible versions)
# optuna==3.4.0  # Temporarily disabled for Railway deployment
# scikit-optimize==0.9.0  # Temporarily disabled for Railway deployment
imbalanced-learn==0.11.0  # Handling imbalanced datasets

# Database (updated to latest compatible versions)
sqlalchemy==2.0.23  # Keep current version for stability
alembic==1.12.1
python-multipart==0.0.6
psycopg2-binary==2.9.9  # PostgreSQL adapter for production

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1

# Development tools
python-dotenv==1.0.0
python-telegram-bot==20.7

# Basketball prediction dependencies
# nba_api==1.2.1  # Free NBA data API - temporarily disabled for Railway deployment
