# Core FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0

# Pydantic v2
pydantic==2.5.0
pydantic-settings==2.1.0

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
bcrypt==4.1.2

# HTTP client
aiohttp==3.9.1
httpx==0.25.2
requests==2.31.0

# Essential data processing (lightweight versions)
pandas==2.1.4
numpy==1.25.2
scikit-learn==1.3.0
joblib==1.3.2

# Database (production ready)
sqlalchemy==2.0.23
alembic==1.12.1
python-multipart==0.0.6
psycopg2-binary==2.9.9

# Essential tools
python-dotenv==1.0.0
python-telegram-bot==20.7

# Testing (minimal)
pytest==7.4.3
pytest-asyncio==0.21.1
