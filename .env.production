# Production Environment Configuration
# Copy this to .env and fill in the actual values

# Application Settings
ENVIRONMENT=production
DEBUG=false
SECRET_KEY=your-super-secret-key-here-change-this-in-production

# Security Settings
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com,api.yourdomain.com
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
API_KEY_HEADER=X-API-Key
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Database Configuration (Production)
DATABASE_URL=postgresql://username:password@localhost:5432/betsightly_prod
DATABASE_ECHO=false
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# External API Keys (Required for production)
FOOTBALL_DATA_API_KEY=your-football-data-api-key
API_FOOTBALL_API_KEY=your-api-football-key
API_FOOTBALL_HOST=api-football-v1.p.rapidapi.com

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
TELEGRAM_GROUP_ID=your-telegram-group-id
TELEGRAM_WEBHOOK_URL=https://yourdomain.com/api/telegram/webhook
TELEGRAM_WEBHOOK_SECRET=your-webhook-secret

# ML Model Configuration
ML_MIN_CONFIDENCE_THRESHOLD=0.70
ML_MAX_PREDICTIONS_PER_CATEGORY=8
ML_PREFERRED_MODELS=xgboost,lightgbm,neural_network,lstm,ensemble
ML_HYPERPARAMETER_OPTIMIZATION=true
ML_FEATURE_SELECTION=true

# Caching Configuration
ML_PREDICTION_CACHE_TTL=3600
ML_FEATURE_CACHE_EXPIRY=24

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=/var/log/betsightly/app.log
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=5

# Performance Settings
WORKERS=4
MAX_CONNECTIONS=1000
KEEPALIVE_TIMEOUT=5

# Monitoring & Health Checks
HEALTH_CHECK_INTERVAL=30
METRICS_ENABLED=true
SENTRY_DSN=your-sentry-dsn-for-error-tracking

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
