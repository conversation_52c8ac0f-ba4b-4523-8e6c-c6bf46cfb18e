services:
  - type: web
    name: betsightly-backend
    env: python
    buildCommand: "pip install --upgrade pip setuptools wheel && pip install -r requirements-render.txt"
    startCommand: "gunicorn main:app --host 0.0.0.0 --port $PORT --workers 2 --worker-class uvicorn.workers.UvicornWorker"
    healthCheckPath: "/api/health"
    envVars:
      - key: ENVIRONMENT
        value: production
      - key: DEBUG
        value: false
      - key: DATABASE_URL
        fromDatabase:
          name: betsightly-db
          property: connectionString
      - key: SECRET_KEY
        generateValue: true
      - key: FOOTBALL_DATA_API_KEY
        sync: false
      - key: API_FOOTBALL_API_KEY
        sync: false
      - key: TELEGRAM_BOT_TOKEN
        sync: false
      - key: ALLOWED_ORIGINS
        value: "http://localhost:5182,https://your-frontend-domain.com"

databases:
  - name: betsightly-db
    databaseName: betsightly
    user: betsightly
    plan: free
