# BetSightly Backend Environment Configuration
# Copy this file to .env and update with your actual values

# =============================================================================
# CRITICAL: API KEYS (REQUIRED)
# =============================================================================

# Football-Data.org API Key (Primary data source)
# Get your free API key from: https://www.football-data.org/client/register
FOOTBALL_DATA_KEY=your_football_data_api_key_here
FOOTBALL_DATA_BASE_URL=https://api.football-data.org/v4
FOOTBALL_DATA_DAILY_LIMIT=100
FOOTBALL_DATA_DEFAULT_COMPETITIONS=PL,PD,SA,BL1,FL1

# API-Football Key (Alternative data source - optional)
# Get your API key from: https://rapidapi.com/api-sports/api/api-football
API_FOOTBALL_KEY=your_api_football_key_here
API_FOOTBALL_HOST=api-football-v1.p.rapidapi.com
API_FOOTBALL_BASE_URL=https://api-football-v1.p.rapidapi.com/v3
API_FOOTBALL_DAILY_LIMIT=100

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Application environment (development, staging, production)
ENVIRONMENT=development

# Debug mode (True for development, False for production)
DEBUG=True

# Application name and version
APP_NAME=BetSightly
APP_VERSION=1.0.0

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Secret key for JWT tokens and encryption (generate a secure random string)
SECRET_KEY=your-super-secret-key-change-this-in-production

# Allowed CORS origins (comma-separated)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,http://localhost:5182

# Trusted hosts for security middleware
TRUSTED_HOSTS=localhost,127.0.0.1,*.betsightly.com

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Database URL (SQLite by default, can be changed to PostgreSQL/MySQL)
DATABASE_URL=sqlite:///./football.db
DATABASE_ECHO=False
DATABASE_POOL_SIZE=5
DATABASE_MAX_OVERFLOW=10

# =============================================================================
# MACHINE LEARNING SETTINGS
# =============================================================================

# Preferred ML models in priority order
ML_PREFERRED_MODELS=xgboost,lightgbm,neural_network,lstm,ensemble

# Model directories
ML_MODEL_DIR=models
ML_DATA_DIR=data
ML_CACHE_DIR=cache

# Model training parameters
ML_TRAIN_TEST_SPLIT=0.2
ML_CROSS_VALIDATION_FOLDS=5
ML_HYPERPARAMETER_OPTIMIZATION=True

# Feature engineering settings
ML_FEATURE_SELECTION=True
ML_MIN_FEATURE_IMPORTANCE=0.01

# Prediction quality thresholds
ML_MIN_CONFIDENCE_THRESHOLD=0.65
ML_MAX_PREDICTIONS_PER_CATEGORY=10

# Caching settings
ML_FEATURE_CACHE_EXPIRY=24
ML_PREDICTION_CACHE_TTL=1800

# =============================================================================
# ODDS CATEGORIES CONFIGURATION
# =============================================================================

# 2-odds category (safe bets)
ODDS_TWO_ODDS_MIN=1.5
ODDS_TWO_ODDS_MAX=2.5
ODDS_TWO_ODDS_MIN_CONFIDENCE=70.0
ODDS_TWO_ODDS_LIMIT=5
ODDS_TWO_ODDS_TARGET=2.0

# 5-odds category (medium risk)
ODDS_FIVE_ODDS_MIN=2.5
ODDS_FIVE_ODDS_MAX=5.0
ODDS_FIVE_ODDS_MIN_CONFIDENCE=70.0
ODDS_FIVE_ODDS_LIMIT=3
ODDS_FIVE_ODDS_TARGET=5.0

# 10-odds category (high risk)
ODDS_TEN_ODDS_MIN=5.0
ODDS_TEN_ODDS_MAX=10.0
ODDS_TEN_ODDS_MIN_CONFIDENCE=70.0
ODDS_TEN_ODDS_LIMIT=2
ODDS_TEN_ODDS_TARGET=10.0

# Rollover category (accumulator bets)
ODDS_ROLLOVER_MIN=1.2
ODDS_ROLLOVER_MAX=2.0
ODDS_ROLLOVER_MIN_CONFIDENCE=70.0
ODDS_ROLLOVER_TARGET=3.0
ODDS_ROLLOVER_DAYS=10

# =============================================================================
# TELEGRAM BOT (OPTIONAL)
# =============================================================================

# Telegram bot token (optional, for notifications)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here
TELEGRAM_WEBHOOK_URL=your_webhook_url_here
TELEGRAM_WEBHOOK_SECRET=your_webhook_secret_here

# =============================================================================
# PERFORMANCE & MONITORING
# =============================================================================

# Rate limiting settings
RATE_LIMIT_MAX_REQUESTS=1000
RATE_LIMIT_WINDOW_SECONDS=3600

# Performance settings
ASYNC_WORKERS=4
MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT=30

# Cache settings
CACHE_ENABLED=True
CACHE_TTL_SECONDS=300
CACHE_MAX_SIZE=1000

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/betsightly.log

# =============================================================================
# NOTES
# =============================================================================

# 1. Never commit this file with real API keys to version control
# 2. Use strong, unique passwords and API keys
# 3. Regularly rotate API keys and secrets
# 4. Monitor API usage to avoid hitting rate limits
# 5. Set up proper logging and monitoring in production
